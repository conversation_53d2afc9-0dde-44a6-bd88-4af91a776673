#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多次变更处理逻辑
"""

from datetime import datetime
from audit_based_bill_generator import AuditBasedBillGenerator

def test_multiple_changes():
    """测试多次变更处理"""
    generator = AuditBasedBillGenerator()
    
    # 模拟多次变更数据
    target_month_changes = [
        {
            'time': datetime(2024, 8, 5),
            'original': {'ADV_I': 2, 'ADV_II': 1},
            'new': {'ADV_I': 4, 'ADV_II': 1}  # 8月5日：ADV_I从2台增加到4台
        },
        {
            'time': datetime(2024, 8, 15),
            'original': {'ADV_I': 4, 'ADV_II': 1},
            'new': {'ADV_I': 4, 'ADV_II': 3}  # 8月15日：ADV_II从1台增加到3台
        },
        {
            'time': datetime(2024, 8, 25),
            'original': {'ADV_I': 4, 'ADV_II': 3},
            'new': {'ADV_I': 2, 'ADV_II': 3}  # 8月25日：ADV_I从4台减少到2台
        }
    ]
    
    # 模拟变化的产品
    changed_products = {
        'ADV_I': {'start': 2, 'end': 2},  # 月初2台，月末2台，中间有变化
        'ADV_II': {'start': 1, 'end': 3}  # 月初1台，月末3台
    }
    
    print("=== 测试多次变更处理 ===")
    print("变更时间线:")
    print("  8月1-4日: ADV_I=2台, ADV_II=1台")
    print("  8月5-14日: ADV_I=4台, ADV_II=1台")
    print("  8月15-24日: ADV_I=4台, ADV_II=3台")
    print("  8月25-31日: ADV_I=2台, ADV_II=3台")
    print()
    
    # 测试多次变更记录创建
    records = generator.create_multiple_change_records(
        "测试用户组", changed_products, target_month_changes, 2024, 8
    )
    
    print("生成的账单记录:")
    for i, record in enumerate(records):
        if record.get('产品详情'):
            print(f"  {i+1}. {record['产品详情']}: {record['数量及单位']}台 "
                  f"({record['开始时间']} - {record['截止时间']}, {record['本月月度使用时长（天）']}天) "
                  f"= {record['总价（元）']}")
    
    return records

def test_mixed_changes():
    """测试混合变更（有变化和不变的产品）"""
    generator = AuditBasedBillGenerator()

    # 模拟资源配置
    resource_configs = [
        {
            'time': datetime(2024, 8, 5),
            'original': {'ADV_I': 2, 'ADV_II': 1, 'ADV_VI': 4},  # ADV_VI不变
            'new': {'ADV_I': 4, 'ADV_II': 1, 'ADV_VI': 4},
            'is_target_month': True
        },
        {
            'time': datetime(2024, 8, 15),
            'original': {'ADV_I': 4, 'ADV_II': 1, 'ADV_VI': 4},
            'new': {'ADV_I': 4, 'ADV_II': 3, 'ADV_VI': 4},
            'is_target_month': True
        }
    ]

    print("\n=== 测试混合变更（有变化和不变产品） ===")
    print("变更时间线:")
    print("  ADV_I: 8月1-4日=2台, 8月5-31日=4台 (有变化)")
    print("  ADV_II: 8月1-14日=1台, 8月15-31日=3台 (有变化)")
    print("  ADV_VI: 整月=4台 (不变)")
    print()

    # 测试完整的账单记录生成
    records = generator.generate_user_bill_records(
        "混合测试用户组", resource_configs, 2024, 8
    )

    print("生成的账单记录:")
    for i, record in enumerate(records):
        if record.get('用户'):
            print(f"  {i+1}. 用户组: {record['用户']}")
        elif record.get('产品详情'):
            print(f"  {i+1}. {record['产品详情']}: {record['数量及单位']}台 "
                  f"({record['开始时间']} - {record['截止时间']}, {record['本月月度使用时长（天）']}天) "
                  f"= {record['总价（元）']}")
        elif record.get('产品类型') == '费用合计':
            print(f"  {i+1}. 费用合计: {record['总价（元）']}")

    return records

if __name__ == "__main__":
    test_multiple_changes()
    test_mixed_changes()
