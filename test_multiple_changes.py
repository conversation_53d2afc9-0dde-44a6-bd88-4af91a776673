#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多次变更处理逻辑
"""

from datetime import datetime
from audit_based_bill_generator import AuditBasedBillGenerator

def test_multiple_changes():
    """测试多次变更处理"""
    generator = AuditBasedBillGenerator()
    
    # 模拟多次变更数据
    target_month_changes = [
        {
            'time': datetime(2024, 8, 5),
            'original': {'ADV_I': 2, 'ADV_II': 1},
            'new': {'ADV_I': 4, 'ADV_II': 1}  # 8月5日：ADV_I从2台增加到4台
        },
        {
            'time': datetime(2024, 8, 15),
            'original': {'ADV_I': 4, 'ADV_II': 1},
            'new': {'ADV_I': 4, 'ADV_II': 3}  # 8月15日：ADV_II从1台增加到3台
        },
        {
            'time': datetime(2024, 8, 25),
            'original': {'ADV_I': 4, 'ADV_II': 3},
            'new': {'ADV_I': 2, 'ADV_II': 3}  # 8月25日：ADV_I从4台减少到2台
        }
    ]
    
    # 模拟变化的产品
    changed_products = {
        'ADV_I': {'start': 2, 'end': 2},  # 月初2台，月末2台，中间有变化
        'ADV_II': {'start': 1, 'end': 3}  # 月初1台，月末3台
    }
    
    print("=== 测试多次变更处理 ===")
    print("变更时间线:")
    print("  8月1-4日: ADV_I=2台, ADV_II=1台")
    print("  8月5-14日: ADV_I=4台, ADV_II=1台")
    print("  8月15-24日: ADV_I=4台, ADV_II=3台")
    print("  8月25-31日: ADV_I=2台, ADV_II=3台")
    print()
    
    # 测试多次变更记录创建
    records = generator.create_multiple_change_records(
        "测试用户组", changed_products, target_month_changes, 2024, 8
    )
    
    print("生成的账单记录:")
    for i, record in enumerate(records):
        if record.get('产品详情'):
            print(f"  {i+1}. {record['产品详情']}: {record['数量及单位']}台 "
                  f"({record['开始时间']} - {record['截止时间']}, {record['本月月度使用时长（天）']}天) "
                  f"= {record['总价（元）']}")
    
    return records

if __name__ == "__main__":
    test_multiple_changes()
