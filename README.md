# 基于Audit Log的账单生成工具

## 项目简介

账单生成工具，基于audit_log.json变更记录生成月度账单。

## 工作原理

### 1. 数据源分析
```
audit_log.json → 提取变更记录 → 识别目标月份变更
     ↓
用户组映射 → 项目集转用户组名称
     ↓
资源解析 → GPU卡数转机器台数
```

### 2. 账单生成流程
```
Step 1: 分析audit_log.json中的变更记录
Step 2: 为有变更的用户组生成分时间段记录
Step 3: 为无变更的用户组使用最近配置整月计费
Step 4: 从上月账单继承没有audit记录的用户组
Step 5: 应用时间过滤，只保留使用到月底的资源
Step 6: 生成完整账单
```

### 3. 分时间段逻辑示例
```
多模态大模型研究中心 ADV_LC_II:
- 7月1日-21日：6.0台 (originalValue)
- 7月22日-31日：5.5台 (newValue)
```

## 安装和使用

### 环境要求
```bash
Python 3.7+
pandas
```

### 安装依赖
```bash
pip install pandas
```

### 快速开始

#### 1. 准备数据文件
确保以下文件存在：
```
docs/
├── audit_log.json                    # 变更记录
├── 用户组名称.csv                     # 用户组映射
└── 院内成本支出25年 - 2025-6.csv       # 上月账单
```

#### 2. 运行生成命令
```bash
python audit_based_bill_generator.py \
  --source "docs/院内成本支出25年 - 2025-6.csv" \
  --target "docs/院内成本支出25年 - 2025-7.csv" \
  --target-year 2025 \
  --target-month 7
```

#### 3. 查看输出
```
基于audit_log.json + 上月账单生成 2025年7月 账单
✅ 加载操作记录: 12 条
从audit_log分析了 5 个用户组
   目标月份变更: 多模态大模型研究中心 (2025/07/22)
   目标月份变更: 多模态交互研究中心 (2025/07/22)
   目标月份变更: 数据平台组 (2025/07/01)
从上月账单继承用户组...
   继承用户组: 认知大模型组 (4 条记录)
   继承用户组: AI框架研究组 (2 条记录)
   ...
生成账单: docs/院内成本支出25年 - 2025-7.csv (89 行)
```

### 参数说明
- `--source`: 上月账单文件路径（用于继承没有audit记录的用户组）
- `--target`: 生成的目标账单文件路径
- `--target-year`: 目标年份
- `--target-month`: 目标月份

## 文件结构

```
.
├── audit_based_bill_generator.py    # 主程序
├── docs/
│   ├── audit_log.json              # 变更记录文件
│   ├── 用户组名称.csv                # 用户组映射表
│   └── 院内成本支出25年 - 2025-*.csv    # 历史账单文件
└── README.md                       # 本文档
```

## 配置说明

### GPU型号映射
```python
gpu_model_map = {
    'NVIDIA_A100-SXM4-40GB': 'ADV_I',
    'NVIDIA_A800-SXM4-80GB': 'ADV_II', 
    'LC_NVIDIA_A800-SXM4-80GB': 'ADV_LC_II',
    'NVIDIA_V100-PCIE-32GB': 'ADV_VI',
    'CAMBRICON_MLU370-X8': 'CAMBRICON_MLU370',
    'NVIDIA_A100-MIG-3g20GB': 'ADV_I_MIG',
    'KUNLUN_R300': 'KUNLUN_R300'
}
```

### 台数计算规则
- **CAMBRICON系列**：卡数 ÷ 16 = 台数
- **其他型号**：卡数 ÷ 8 = 台数

## 注意事项

### 数据依赖
1. **audit_log.json**：必须包含完整的变更记录
2. **用户组映射表**：确保项目集到用户组的映射完整
3. **上月账单**：用于继承没有audit记录的用户组

### 限制说明
1. **存储记录**：当前版本不处理存储相关费用
2. **价格精度**：可能需要根据实际单价调整计算公式
3. **复杂变更**：目前只处理单次变更，复杂的多次变更需要扩展

## 技术细节

### audit_log.json数据结构
```json
{
  "items": [
    {
      "ownerName": "baai-vision",
      "createdTime": "1721635253868",
      "originalValue": [
        {
          "priority": "high",
          "resourceDetail": {
            "acceleratorModel": "LC_NVIDIA_A800-SXM4-80GB",
            "acceleratorCount": 48
          }
        }
      ],
      "newValue": [
        {
          "priority": "high",
          "resourceDetail": {
            "acceleratorModel": "LC_NVIDIA_A800-SXM4-80GB",
            "acceleratorCount": 44
          }
        }
      ]
    }
  ]
}
```

### 生成的账单格式
```csv
用户,产品类型,产品详情,数量及单位,使用方式,本月月度使用时长（天）,开始时间,截止时间,单价及单位,总价（元）,备注
多模态大模型研究中心,算力,ADV_LC_II,6.0,算力平台调用,21,2025/7/1,2025/7/21,50000,"¥203,225.81",
,算力,ADV_LC_II,5.5,算力平台调用,10,2025/7/22,2025/7/31,50000,"¥88,709.68",
,费用合计,,,,,,,,¥5,385,685.48,
```

## 数据流图

```
audit_log.json
     ↓
[时间过滤] → 筛选目标月份记录
     ↓
[用户映射] → 项目集 → 用户组名称
     ↓
[资源解析] → GPU型号 → 标准化名称
     ↓
[台数计算] → 卡数 → 机器台数
     ↓
[变更检测] → originalValue vs newValue
     ↓
[分时间段] → 变更前 + 变更后
     ↓
[资源继承] → 上月账单 → 补充缺失用户组
     ↓
[时间过滤] → 只保留使用到月底的资源
     ↓
[账单生成] → CSV格式输出
```
