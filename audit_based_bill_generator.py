#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于audit_log.json的账单生成工具
"""

import pandas as pd
import json
import argparse
import os
from datetime import datetime
import calendar
from collections import defaultdict

class AuditBasedBillGenerator:
    def __init__(self):
        self.user_group_mapping = self.load_user_group_mapping()
        self.pricing_data = self.load_pricing_data()
        
    def load_user_group_mapping(self):
        """加载用户组映射"""
        mapping_file = 'docs/用户组名称.csv'
        if os.path.exists(mapping_file):
            df = pd.read_csv(mapping_file)
            mapping = {}
            for _, row in df.iterrows():
                if len(df.columns) >= 2:
                    user_group = row.iloc[0]  # 用户组名称
                    project_set = row.iloc[1]  # 项目集
                    if pd.notna(user_group) and pd.notna(project_set):
                        mapping[project_set] = user_group
            return mapping
        return {}

    def load_pricing_data(self):
        """加载产品定价数据"""
        pricing_file = 'docs/定价.csv'
        pricing = {}

        if os.path.exists(pricing_file):
            try:
                df = pd.read_csv(pricing_file)
                for _, row in df.iterrows():
                    product = row['产品型号']
                    price = float(row['单价'])
                    pricing[product] = price
                print(f"✅ 加载定价数据: {len(pricing)} 个产品")
            except Exception as e:
                print(f"❌ 加载定价数据失败: {e}")
        else:
            print(f"❌ 定价文件不存在: {pricing_file}")
            # 使用默认定价
            pricing = {
                'ADV_I': 50000.0,
                'ADV_II': 50000.0,
                'ADV_VI': 22500.0,
                'ADV_LC_II': 50000.0,
                'CAMBRICON_MLU370': 12000.0,
                '宏杉高速存储': 500.0,
                '浪潮低速存储': 100.0
            }

        return pricing

    def get_product_price(self, product_name):
        """获取产品单价"""
        return self.pricing_data.get(product_name, 50000.0)  # 默认5万

    def get_days_in_month(self, year, month):
        """获取指定月份的天数"""
        return calendar.monthrange(year, month)[1]
    
    def load_audit_logs(self, audit_log_file='docs/audit_log.json'):
        """加载操作记录"""
        try:
            if os.path.exists(audit_log_file):
                with open(audit_log_file, 'r', encoding='utf-8') as f:
                    audit_data = json.load(f)
                print(f"✅ 加载操作记录: {len(audit_data.get('items', []))} 条")
                return audit_data
            else:
                print(f"❌ 操作记录文件不存在: {audit_log_file}")
                return None
        except Exception as e:
            print(f"❌ 加载操作记录失败: {e}")
            return None
    
    def extract_gpu_resources(self, resources):
        """提取GPU资源，根据型号不同除以不同的数值得到台数"""
        gpu_model_map = {
            'NVIDIA_A100-SXM4-40GB': 'ADV_I',
            'NVIDIA_A800-SXM4-80GB': 'ADV_II',
            'LC_NVIDIA_A800-SXM4-80GB': 'ADV_LC_II',
            'NVIDIA_V100-PCIE-32GB': 'ADV_VI',
            'CAMBRICON_MLU370-X8': 'CAMBRICON_MLU370',
            'NVIDIA_A100-MIG-3g20GB': 'ADV_I_MIG',
            'KUNLUN_R300': 'KUNLUN_R300'
        }

        gpus = {}

        for resource in resources:
            if resource.get('priority') == 'high' and 'resourceDetail' in resource:
                detail = resource['resourceDetail']
                model = detail.get('acceleratorModel')
                count = detail.get('acceleratorCount', 0)

                if model in gpu_model_map and count > 0:
                    gpu_type = gpu_model_map[model]

                    # CAMBRICON系列除以16，其他型号除以8
                    if model.startswith('CAMBRICON_'):
                        machine_count = count / 16
                    else:
                        machine_count = count / 8

                    gpus[gpu_type] = gpus.get(gpu_type, 0) + machine_count

        return gpus
    
    def analyze_user_resources(self, audit_data, target_year, target_month):
        """分析每个用户组的资源配置"""
        if not audit_data or 'items' not in audit_data:
            return {}
        
        # 目标月份的时间范围
        target_start = datetime(target_year, target_month, 1)
        if target_month == 12:
            target_end = datetime(target_year + 1, 1, 1)
        else:
            target_end = datetime(target_year, target_month + 1, 1)
        
        user_resources = defaultdict(list)  # {user_group: [resource_configs]}
        
        # 按时间排序处理所有记录
        sorted_items = sorted(audit_data['items'], 
                            key=lambda x: int(x['createdTime']))
        
        for item in sorted_items:
            timestamp = int(item['createdTime'])
            created_time = datetime.fromtimestamp(timestamp / 1000)
            
            owner_name = item['ownerName']
            user_group = self.user_group_mapping.get(owner_name)
            
            if not user_group:
                continue
            
            # 提取资源配置
            original_gpus = self.extract_gpu_resources(item.get('originalValue', []))
            new_gpus = self.extract_gpu_resources(item.get('newValue', []))
            
            # 记录资源配置
            config = {
                'time': created_time,
                'original': original_gpus,
                'new': new_gpus,
                'is_target_month': target_start <= created_time < target_end
            }
            
            user_resources[user_group].append(config)
            
            if config['is_target_month']:
                print(f"   目标月份变更: {user_group} ({created_time.strftime('%Y/%m/%d')})")
        
        return user_resources
    
    def generate_user_bill_records(self, user_group, resource_configs, target_year, target_month):
        """为单个用户组生成账单记录"""
        target_days = self.get_days_in_month(target_year, target_month)
        target_start = datetime(target_year, target_month, 1)
        target_end = datetime(target_year, target_month + 1, 1) if target_month < 12 else datetime(target_year + 1, 1, 1)
        
        # 找到目标月份的变更
        target_month_changes = [config for config in resource_configs if config['is_target_month']]

        # 找到这个用户组最近的配置（不管什么时候的）
        latest_config = resource_configs[-1] if resource_configs else None  # 最后一条记录就是最新的

        print(f"     最近配置时间: {latest_config['time'] if latest_config else '无'}")

        records = []

        if not target_month_changes:
            # 目标月份没有变更，使用最近配置整月
            if latest_config:
                gpus = latest_config['new']  # 使用最近变更的newValue
                print(f"   {user_group}: 无变更，使用最近配置的newValue")
                records.extend(self.create_full_month_records(user_group, gpus, target_year, target_month))
        else:
            # 目标月份有变更，分别处理有变化和无变化的产品
            print(f"   {user_group}: 有{len(target_month_changes)}个变更")

            # 确定月初配置：使用第一个变更的originalValue
            first_change = target_month_changes[0]
            month_start_config = first_change['original']
            month_end_config = target_month_changes[-1]['new']

            # 找出真正有变化的产品
            changed_products = {}
            unchanged_products = {}

            for product in set(list(month_start_config.keys()) + list(month_end_config.keys())):
                start_qty = month_start_config.get(product, 0)
                end_qty = month_end_config.get(product, 0)

                if start_qty != end_qty:
                    changed_products[product] = {'start': start_qty, 'end': end_qty}
                elif end_qty > 0:  # 只记录有配额的不变产品
                    unchanged_products[product] = end_qty

            print(f"     变化的产品: {changed_products}")
            print(f"     不变的产品: {unchanged_products}")

            # 为有变化的产品创建分时间段记录
            first_record = True
            for change in target_month_changes:
                change_day = change['time'].day

                # 变更前时间段
                if change_day > 1:
                    period_days = change_day - 1
                    changed_config_before = {p: info['start'] for p, info in changed_products.items() if info['start'] > 0}
                    if changed_config_before:
                        period_records = self.create_period_records(
                            user_group if first_record else '', changed_config_before, target_year, target_month,
                            1, change_day - 1, period_days
                        )
                        records.extend(period_records)
                        first_record = False

                # 变更后时间段
                period_days = target_days - change_day + 1
                changed_config_after = {p: info['end'] for p, info in changed_products.items() if info['end'] > 0}
                if changed_config_after:
                    period_records = self.create_period_records(
                        user_group if first_record else '', changed_config_after, target_year, target_month,
                        change_day, target_days, period_days
                    )
                    records.extend(period_records)
                    first_record = False

                break  # 只处理第一个变更，简化逻辑

            # 为不变的产品创建整月记录
            if unchanged_products:
                full_month_records = self.create_full_month_records(
                    user_group if first_record else '', unchanged_products, target_year, target_month
                )
                # 只添加产品记录，不添加用户行
                product_records = [r for r in full_month_records if r['产品类型'] == '算力']
                records.extend(product_records)

            # 添加费用合计（算力费用总和，存储费用会单独添加）
            total_cost = sum(float(record['总价（元）'].replace('¥', '').replace(',', ''))
                           for record in records if record['产品类型'] == '算力')
            records.append({
                '用户': '',
                '产品类型': '费用合计',
                '产品详情': '',
                '数量及单位': '',
                '使用方式': '',
                '本月月度使用时长（天）': '',
                '开始时间': '',
                '截止时间': '',
                '单价及单位': '',
                '价格单位': '',
                '总价（元）': f'¥{total_cost:,.2f}',
                '备注': ''
            })
        
        # 检查是否只有组名和费用合计，没有实际明细
        detail_records = [r for r in records if r.get('产品类型') in ['算力', '存储']]
        if not detail_records:
            print(f"   {user_group}: 无有效资源明细，需要继承上月账单")
            return None
        
        return records
    
    def create_full_month_records(self, user_group, gpus, target_year, target_month):
        """创建整月记录"""
        records = []
        target_days = self.get_days_in_month(target_year, target_month)
        
        # 用户行
        records.append({
            '用户': user_group,
            '产品类型': '',
            '产品详情': '',
            '数量及单位': '',
            '单位': '',
            '使用方式': '',
            '本月月度使用时长（天）': '',
            '开始时间': '',
            '截止时间': '',
            '单价及单位': '',
            '价格单位': '',
            '总价（元）': '',
            '备注': ''
        })
        
        # 产品行
        for gpu_type, quantity in gpus.items():
            if quantity > 0:
                # 从定价文件获取单价
                unit_price = self.get_product_price(gpu_type)

                records.append({
                    '用户': '',
                    '产品类型': '算力',
                    '产品详情': gpu_type,
                    '数量及单位': quantity,
                    '单位': '台',
                    '使用方式': '算力平台调用',
                    '本月月度使用时长（天）': target_days,
                    '开始时间': f'{target_year}/{target_month}/1',
                    '截止时间': f'{target_year}/{target_month}/{target_days}',
                    '单价及单位': unit_price,
                    '价格单位': '元/台/月',
                    '总价（元）': f'¥{quantity * unit_price:,.2f}',
                    '备注': ''
                })

        # 费用合计行（只包含算力费用，存储费用会在最终合计时添加）
        total_cost = 0
        for gpu_type, quantity in gpus.items():
            if quantity > 0:
                unit_price = self.get_product_price(gpu_type)
                total_cost += quantity * unit_price

        records.append({
            '用户': '',
            '产品类型': '费用合计',
            '产品详情': '',
            '数量及单位': '',
            '单位': '',
            '使用方式': '',
            '本月月度使用时长（天）': '',
            '开始时间': '',
            '截止时间': '',
            '单价及单位': '',
            '价格单位': '',
            '总价（元）': f'¥{total_cost:,.2f}',
            '备注': ''
        })
        
        return records
    
    def create_period_records(self, user_group, gpus, target_year, target_month, start_day, end_day, period_days):
        """创建时间段记录"""
        records = []
        
        for gpu_type, quantity in gpus.items():
            if quantity > 0:
                # 从定价文件获取单价
                unit_price = self.get_product_price(gpu_type)

                records.append({
                    '用户': user_group if len(records) == 0 else '',
                    '产品类型': '算力',
                    '产品详情': gpu_type,
                    '数量及单位': quantity,
                    '单位': '台',
                    '使用方式': '算力平台调用',
                    '本月月度使用时长（天）': period_days,
                    '开始时间': f'{target_year}/{target_month}/{start_day}',
                    '截止时间': f'{target_year}/{target_month}/{end_day}',
                    '单价及单位': unit_price,
                    '价格单位': '元/台/月',
                    '总价（元）': f'¥{quantity * unit_price * period_days / self.get_days_in_month(target_year, target_month):,.2f}',
                    '备注': ''
                })
        
        return records
    
    def generate_bill(self, target_file, target_year, target_month, source_file=None):
        """生成账单 - 分组文件方案：每个用户组单独文件 + 最终合并"""
        print(f"基于audit_log.json + 上月账单生成 {target_year}年{target_month}月 账单")

        # 创建月份文件夹
        month_folder = f"{target_year}-{target_month:02d}"
        os.makedirs(month_folder, exist_ok=True)
        print(f"创建月份文件夹: {month_folder}/")

        # 1. 加载audit logs
        audit_data = self.load_audit_logs()
        if not audit_data:
            return None

        # 2. 分析有audit记录的用户资源配置
        user_resources = self.analyze_user_resources(audit_data, target_year, target_month)
        print(f"从audit_log分析了 {len(user_resources)} 个用户组")

        # 3. 从存储文件中提取存储数据
        self.target_year = target_year
        self.target_month = target_month
        storage_data = self.extract_storage_data_from_source(source_file)
        print(f"提取存储数据: {len(storage_data)} 个用户组")

        # 4. 为有audit记录的用户组生成单独文件
        all_user_records = {}
        audit_users = set()
        empty_audit_users = set()

        for user_group, resource_configs in user_resources.items():
            user_records = self.generate_user_bill_records(user_group, resource_configs, target_year, target_month)

            # 检查是否所有newValue都为空（无有效资源）
            all_new_empty = all((not c['new']) for c in resource_configs)
            if (user_records is None or (len(user_records) <= 2 and all_new_empty)):
                # 标记为需要继承
                print(f"[audit记录无有效资源] {user_group} 需要继承上月账单")
                empty_audit_users.add(user_group)
                continue

            # 添加存储记录
            if user_group in storage_data:
                storage_records = self.create_storage_records(user_group, storage_data[user_group], target_year, target_month)
                user_records.extend(storage_records)

            # 重新计算费用合计（包含算力+存储）
            user_records = self.recalculate_user_total(user_records)

            # 保存单独文件
            self.save_user_group_file(user_group, user_records, month_folder)
            all_user_records[user_group] = user_records
            audit_users.add(user_group)

        # 5. 处理没有audit记录的用户组（从上月账单继承）
        inherited_user_records = {}
        if source_file and os.path.exists(source_file):
            source_year = target_year
            source_month = target_month - 1
            if source_month == 0:
                source_month = 12
                source_year -= 1

            # 先找出所有上月账单的用户组
            source_df = pd.read_csv(source_file)
            all_source_users = set(source_df['用户'].dropna().unique())
            # 没有audit记录的用户组 + audit记录但无有效资源的用户组
            no_audit_users = (all_source_users - audit_users) | empty_audit_users

            # 继承这些组的全部资源明细（包括费用为0的组）
            for user in no_audit_users:
                user_rows = source_df[source_df['用户'] == user].to_dict(orient='records')
                if user_rows and any(r.get('产品类型') in ['算力', '存储'] for r in user_rows):
                    print(f"[无audit记录或无有效资源] 直接继承上月账单: {user} ({len(user_rows)} 条)")
                    inherited_user_records[user] = user_rows
                    # 保存单独文件
                    self.save_user_group_file(user, user_rows, month_folder)
            # 兼容原有继承逻辑
            inherited_user_records.update(self.inherit_from_previous_bill_by_group(
                source_file, audit_users, source_year, source_month, target_year, target_month, month_folder
            ))
            all_user_records.update(inherited_user_records)

        # 6. 合并所有用户组文件为完整账单
        final_records = self.merge_all_user_files(all_user_records)

        # 7. 保存最终合并文件
        df = pd.DataFrame(final_records)
        df.to_csv(target_file, index=False, encoding='utf-8-sig')
        print(f"生成完整账单: {target_file} ({len(df)} 行)")

        return df

    def inherit_from_previous_bill(self, source_file, audit_users, source_year, source_month, target_year, target_month):
        """从上月账单继承没有audit记录的用户组"""
        print(f"从上月账单继承用户组...")

        source_df = pd.read_csv(source_file)
        source_days = self.get_days_in_month(source_year, source_month)
        target_days = self.get_days_in_month(target_year, target_month)

        inherited_records = []
        current_user = None
        user_records = []

        for idx, row in source_df.iterrows():
            # 更新当前用户
            if pd.notna(row['用户']) and row['用户'] != '':
                # 处理上一个用户的记录
                if current_user and current_user not in audit_users:
                    filtered_records = self.filter_continuing_resources(user_records, source_year, source_month, source_days)
                    if filtered_records:
                        updated_records = self.update_inherited_records(filtered_records, current_user, target_year, target_month, target_days)
                        inherited_records.extend(updated_records)
                        print(f"   继承用户组: {current_user} ({len(updated_records)} 条记录)")

                # 开始新用户
                current_user = row['用户']
                user_records = [row]
                continue

            # 收集当前用户的记录
            if current_user:
                user_records.append(row)

        # 处理最后一个用户
        if current_user and current_user not in audit_users:
            filtered_records = self.filter_continuing_resources(user_records, source_year, source_month, source_days)
            if filtered_records:
                updated_records = self.update_inherited_records(filtered_records, current_user, target_year, target_month, target_days)
                inherited_records.extend(updated_records)
                print(f"   继承用户组: {current_user} ({len(updated_records)} 条记录)")

        return inherited_records

    def filter_continuing_resources(self, user_records, source_year, source_month, source_days):
        """过滤延续到下个月的资源：只保留使用到月底的算力资源"""
        continuing_records = []

        for record in user_records:
            if record['产品类型'] == '费用合计':
                continue  # 跳过费用合计，后面重新计算

            # 只有纯用户行（没有产品详情）才直接保留
            if (pd.notna(record['用户']) and record['用户'] != '' and
                (pd.isna(record.get('产品详情', '')) or record.get('产品详情', '') == '')):
                continuing_records.append(record)  # 纯用户行
                continue

            # 存储记录直接保留
            if '存储' in str(record.get('产品详情', '')):
                continuing_records.append(record)
                continue

            # 检查算力产品行是否使用到月底
            if self.is_resource_used_until_month_end(record, source_year, source_month, source_days):
                continuing_records.append(record)
            else:
                product = record.get('产品详情', '')
                end_time = record.get('截止时间', '')
                print(f"       过滤: {product} (结束时间: {end_time})")

        return continuing_records

    def is_resource_used_until_month_end(self, record, source_year, source_month, source_days):
        """判断资源是否使用到月底"""
        end_time = str(record['截止时间']) if pd.notna(record['截止时间']) else ""

        # 提取结束日期
        import re
        end_day_match = re.search(rf'{source_year}[/-]0?{source_month}[/-](\d+)', end_time)
        if end_day_match:
            end_day = int(end_day_match.group(1))
            return end_day == source_days  # 使用到月底

        return True  # 如果无法解析，默认延续

    def update_inherited_records(self, records, user_group, target_year, target_month, target_days):
        """更新继承记录的时间信息"""
        updated_records = []
        total_cost = 0

        for record in records:
            updated_record = record.copy()

            if record['产品类型'] != '费用合计':
                # 更新时间信息
                updated_record['本月月度使用时长（天）'] = target_days
                updated_record['开始时间'] = f'{target_year}/{target_month}/1'
                updated_record['截止时间'] = f'{target_year}/{target_month}/{target_days}'

                # 添加单位列和修正使用方式
                product = record['产品详情']
                if '存储' in str(product):
                    updated_record['单位'] = 'T'
                    updated_record['使用方式'] = '存储服务'  # 修正存储的使用方式
                    price_unit = '元/T/月'  # 存储的单价单位
                else:
                    updated_record['单位'] = '台'
                    price_unit = '元/台/月'  # 算力的单价单位

                # 从定价文件获取单价
                unit_price = self.get_product_price(product)
                updated_record['单价及单位'] = unit_price
                updated_record['价格单位'] = price_unit

                # 重新计算价格
                quantity = record['数量及单位']
                if (pd.notna(quantity) and isinstance(quantity, (int, float))):
                    try:
                        if '存储' in str(product):
                            # 存储按月计费
                            cost = quantity * unit_price
                        else:
                            # 算力按月计费（整月）
                            cost = quantity * unit_price
                        updated_record['总价（元）'] = f'¥{cost:,.2f}'
                        total_cost += cost
                    except:
                        pass
            else:
                # 费用合计行也需要添加单位列
                updated_record['单位'] = ''
                updated_record['价格单位'] = ''

            updated_records.append(updated_record)

        # 添加费用合计
        updated_records.append({
            '用户': '',
            '产品类型': '费用合计',
            '产品详情': '',
            '数量及单位': '',
            '使用方式': '',
            '本月月度使用时长（天）': '',
            '开始时间': '',
            '截止时间': '',
            '单价及单位': '',
            '总价（元）': f'¥{total_cost:,.2f}',
            '备注': ''
        })

        return updated_records

    def extract_storage_data_from_source(self, source_file):
        """从存储文件中提取存储数据"""
        storage_data = {}

        # 1. 读取宏杉高速存储数据
        high_speed_file = f'docs/存储使用详情.csv'
        if os.path.exists(high_speed_file):
            try:
                df = pd.read_csv(high_speed_file)
                for _, row in df.iterrows():
                    project_set = row['项目集']
                    usage_tb = row['使用量(TB)']

                    # 转换项目集到用户组
                    user_group = self.user_group_mapping.get(project_set)
                    if user_group and usage_tb > 0:
                        if user_group not in storage_data:
                            storage_data[user_group] = []

                        storage_data[user_group].append({
                            'product': '宏杉高速存储',
                            'quantity': usage_tb,
                            'usage': '存储服务',
                            'unit_price': self.get_product_price('宏杉高速存储')
                        })
                print(f"✅ 加载宏杉高速存储数据: {len([u for u in storage_data.values() if any('宏杉' in s['product'] for s in u)])} 个用户组")
            except Exception as e:
                print(f"❌ 加载宏杉高速存储数据失败: {e}")

        # 2. 读取浪潮低速存储数据
        low_speed_file = 'docs/浪潮低速存储.csv'
        if os.path.exists(low_speed_file):
            try:
                df = pd.read_csv(low_speed_file)
                for _, row in df.iterrows():
                    project_set = row['九鼎项目集']
                    usage_tb = row['总存储（TB ）']

                    # 转换项目集到用户组
                    user_group = self.user_group_mapping.get(project_set)
                    if user_group and usage_tb > 0:
                        if user_group not in storage_data:
                            storage_data[user_group] = []

                        storage_data[user_group].append({
                            'product': '浪潮低速存储',
                            'quantity': usage_tb,
                            'usage': '存储服务',
                            'unit_price': self.get_product_price('浪潮低速存储')
                        })
                print(f"✅ 加载浪潮低速存储数据: {len([u for u in storage_data.values() if any('浪潮' in s['product'] for s in u)])} 个用户组")
            except Exception as e:
                print(f"❌ 加载浪潮低速存储数据失败: {e}")

        return storage_data

    def create_storage_records(self, user_group, storage_list, target_year, target_month):
        """创建存储记录"""
        records = []
        target_days = self.get_days_in_month(target_year, target_month)

        for storage in storage_list:
            quantity = storage['quantity']
            product_name = storage['product']

            # 从定价文件获取存储单价
            unit_price = self.get_product_price(product_name)

            # 计算价格
            total_cost = quantity * unit_price

            records.append({
                '用户': '',
                '产品类型': '存储',
                '产品详情': product_name,
                '数量及单位': quantity,
                '单位': 'T',
                '使用方式': storage['usage'],
                '本月月度使用时长（天）': target_days,
                '开始时间': f'{target_year}/{target_month}/1',
                '截止时间': f'{target_year}/{target_month}/{target_days}',
                '单价及单位': unit_price,
                '价格单位': '元/T/月',
                '总价（元）': f'¥{total_cost:,.2f}',
                '备注': ''
            })

        return records

    def save_user_group_file(self, user_group, user_records, month_folder):
        """保存单个用户组的文件"""
        # 清理文件名中的特殊字符
        safe_filename = user_group.replace('/', '_').replace('\\', '_').replace(':', '_')
        file_path = os.path.join(month_folder, f"{safe_filename}.csv")

        # 确保user_records是字典列表格式
        if user_records:
            try:
                # 统一转换为字典列表
                converted_records = []
                for record in user_records:
                    if isinstance(record, pd.Series):
                        converted_records.append(record.to_dict())
                    elif isinstance(record, dict):
                        converted_records.append(record)
                    else:
                        print(f"警告: 未知记录类型: {type(record)}")
                        continue

                # 创建DataFrame并确保列顺序正确
                df = pd.DataFrame(converted_records)

                # 定义正确的列顺序
                correct_columns = [
                    '用户', '产品类型', '产品详情', '数量及单位', '单位', '使用方式',
                    '本月月度使用时长（天）', '开始时间', '截止时间', '单价及单位', '价格单位', '总价（元）', '备注'
                ]

                # 重新排列列顺序，只保留存在的列
                existing_columns = [col for col in correct_columns if col in df.columns]
                df = df[existing_columns]

                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                print(f"   保存用户组文件: {file_path} ({len(converted_records)} 行)")
            except Exception as e:
                print(f"警告: 创建DataFrame失败: {e}")
                return
        else:
            print(f"警告: {user_group} 没有记录")

    def inherit_from_previous_bill_by_group(self, source_file, audit_users, source_year, source_month, target_year, target_month, month_folder):
        """从上月账单按用户组继承"""
        print(f"从上月账单继承用户组...")

        source_df = pd.read_csv(source_file)
        source_days = self.get_days_in_month(source_year, source_month)
        target_days = self.get_days_in_month(target_year, target_month)

        inherited_user_records = {}
        current_user = None
        user_records = []

        for idx, row in source_df.iterrows():
            # 更新当前用户
            if pd.notna(row['用户']) and row['用户'] != '':
                # 处理上一个用户的记录
                if current_user and current_user not in audit_users:
                    print(f"[继承] 正在处理用户组: {current_user}, 记录数: {len(user_records)}")
                    filtered_records = self.filter_continuing_resources(user_records, source_year, source_month, source_days)
                    print(f"[继承] 过滤后剩余记录数: {len(filtered_records)}")
                    if filtered_records:
                        updated_records = self.update_inherited_records(filtered_records, current_user, target_year, target_month, target_days)
                        print(f"[继承] 更新后记录数: {len(updated_records)}，内容: {updated_records}")
                        # 重新计算费用合计（包含算力+存储）
                        updated_records = self.recalculate_user_total(updated_records)
                        # 保存单独文件
                        self.save_user_group_file(current_user, updated_records, month_folder)
                        inherited_user_records[current_user] = updated_records
                        print(f"   继承用户组: {current_user} ({len(updated_records)} 条记录)")
                    else:
                        print(f"[继承] 用户组 {current_user} 过滤后无可继承记录")
                # 开始新用户
                current_user = row['用户']
                user_records = [row]
                continue
            # 收集当前用户的记录
            if current_user:
                user_records.append(row)

        # 处理最后一个用户
        if current_user and current_user not in audit_users:
            print(f"[继承] 正在处理用户组: {current_user}, 记录数: {len(user_records)}")
            filtered_records = self.filter_continuing_resources(user_records, source_year, source_month, source_days)
            print(f"[继承] 过滤后剩余记录数: {len(filtered_records)}")
            if filtered_records:
                updated_records = self.update_inherited_records(filtered_records, current_user, target_year, target_month, target_days)
                print(f"[继承] 更新后记录数: {len(updated_records)}，内容: {updated_records}")
                # 重新计算费用合计（包含算力+存储）
                updated_records = self.recalculate_user_total(updated_records)
                # 保存单独文件
                self.save_user_group_file(current_user, updated_records, month_folder)
                inherited_user_records[current_user] = updated_records
                print(f"   继承用户组: {current_user} ({len(updated_records)} 条记录)")
            else:
                print(f"[继承] 用户组 {current_user} 过滤后无可继承记录")

        return inherited_user_records

    def merge_all_user_files(self, all_user_records):
        """合并所有用户组记录为完整账单"""
        print(f"合并所有用户组文件...")

        final_records = []
        for user_group, user_records in all_user_records.items():
            final_records.extend(user_records)
            print(f"   合并: {user_group} ({len(user_records)} 行)")

        return final_records

    def recalculate_user_total(self, user_records):
        """重新计算用户组的费用合计（算力+存储），并将费用合计移到最后"""
        total_cost = 0

        # 计算所有费用（算力+存储）
        for record in user_records:
            if record['产品类型'] in ['算力', '存储']:
                try:
                    cost_str = str(record['总价（元）']).replace('¥', '').replace(',', '')
                    cost = float(cost_str)
                    total_cost += cost
                except:
                    pass

        # 移除原有的费用合计行
        user_records = [record for record in user_records if record['产品类型'] != '费用合计']

        # 在最后添加新的费用合计行
        user_records.append({
            '用户': '',
            '产品类型': '费用合计',
            '产品详情': '',
            '数量及单位': '',
            '单位': '',
            '使用方式': '',
            '本月月度使用时长（天）': '',
            '开始时间': '',
            '截止时间': '',
            '单价及单位': '',
            '价格单位': '',
            '总价（元）': f'¥{total_cost:,.2f}',
            '备注': ''
        })

        return user_records

def main():
    parser = argparse.ArgumentParser(description='基于audit_log.json的账单生成工具')
    parser.add_argument('--target', required=True, help='目标账单文件路径')
    parser.add_argument('--target-year', type=int, required=True, help='目标年份')
    parser.add_argument('--target-month', type=int, required=True, help='目标月份')
    parser.add_argument('--source', help='上月账单文件路径（用于继承没有audit记录的用户组）')

    args = parser.parse_args()

    generator = AuditBasedBillGenerator()
    generator.generate_bill(args.target, args.target_year, args.target_month, args.source)

if __name__ == "__main__":
    main()
